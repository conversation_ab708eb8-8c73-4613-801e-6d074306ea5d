import apiClient from '../utils/axios';

export interface HoD {
  _id: string;
  name: string;
  email: string;
  department: string;
  academicYear: string;
  facultyID: string;
}

export interface CreateHoDCredentialsRequest {
  departments: string[];
  academicYear: string;
}

export interface AssignHoDRequest {
  department: string;
  academicYear: string;
  facultyId: string;
}

export const hodService = {
  // Get all HoDs for a specific academic year
  getHoDs: async (academicYear: string): Promise<HoD[]> => {
    const response = await apiClient.get(`/api/hod-credentials/hods?academicYear=${academicYear}`);
    return response.data as HoD[];
  },

  // Create HoD credentials for departments
  createHoDCredentials: async (data: CreateHoDCredentialsRequest): Promise<any> => {
    // Backend expects { HODs: [...] }
    const payload = { HODs: data.departments, academicYear: data.academicYear };
    const response = await apiClient.post('/api/hod-credentials/create', payload);
    return response.data;
  },

  // Assign faculty to HoD role
  assignHoD: async (data: AssignHoDRequest): Promise<any> => {
    const response = await apiClient.post('/api/hod-credentials/assign', data);
    return response.data;
  },

  // Reset HoD password
  resetHoDPassword: async (hodId: string): Promise<any> => {
    const response = await apiClient.put(`/api/hod-credentials/reset-password/${hodId}`);
    return response.data;
  },
};

export default hodService;

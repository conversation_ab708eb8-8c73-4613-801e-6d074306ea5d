import express from "express";
import Department from "../models/Department.js";
import { protect } from "../middleware/authMiddleware.js";
import { isAdmin } from "../middleware/roleMiddleware.js";
import { validateDepartment, validateObjectId, validatePagination } from "../middleware/validation.js";
import { paginateQuery } from "../utils/pagination.js";

const router = express.Router();

// Get department names (for dropdowns)
router.get("/names", validatePagination, async (req, res, next) => {
  try {
    const { includeInactive } = req.query;
    const filter = includeInactive === "true" ? {} : { isActive: true };

    const departments = await Department.find(filter)
      .select("name abbreviation")
      .sort({ name: 1 });

    // Return just the names as strings for dropdown compatibility
    const departmentNames = departments.map(dept => dept.name);
    res.json(departmentNames);
  } catch (error) {
    next(error);
  }
});

// Get all departments (alternative endpoint)
router.get("/all", async (req, res, next) => {
  try {
    const { includeInactive } = req.query;
    const filter = includeInactive === "true" ? {} : { isActive: true };

    const departments = await Department.find(filter)
      .select("name abbreviation isActive")
      .sort({ name: 1 });

    res.json(departments);
  } catch (error) {
    next(error);
  }
});

// Protected routes
router.use(protect);

// Get all departments
router.get("/", validatePagination, async (req, res, next) => {
  try {
    const { includeInactive } = req.query;
    const queryWithFilter = { ...req.query };
    if (includeInactive !== "true") {
      queryWithFilter.isActive = true;
    }

    const result = await paginateQuery(Department, queryWithFilter, {
      allowedFilters: ['name', 'abbreviation', 'isActive', 'search'],
      allowedSortFields: ['name', 'abbreviation', 'createdAt', 'updatedAt'],
      defaultSort: { name: 1 },
      populate: [{ path: 'createdBy', select: 'name email' }]
    });

    // Return just the data array for frontend compatibility
    res.json(result.data);
  } catch (error) {
    next(error);
  }
});

// Get department usage statistics
router.get("/:id/usage", validateObjectId("id"), async (req, res, next) => {
  try {
    // Placeholder implementation
    res.json({
      success: true,
      data: {
        departmentId: req.params.id,
        facultyCount: 0,
        studentCount: 0,
        courseCount: 0
      }
    });
  } catch (error) {
    next(error);
  }
});

// Admin only routes
router.use(isAdmin);

// Create new department
router.post("/", validateDepartment, async (req, res, next) => {
  try {
    const department = new Department({
      ...req.body,
      createdBy: req.user._id
    });
    await department.save();

    res.status(201).json({
      success: true,
      data: department,
      message: "Department created successfully"
    });
  } catch (error) {
    next(error);
  }
});

// Update department
router.put("/:id", validateObjectId("id"), validateDepartment, async (req, res, next) => {
  try {
    const department = await Department.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!department) {
      return res.status(404).json({
        success: false,
        error: "Department not found"
      });
    }

    res.json({
      success: true,
      data: department,
      message: "Department updated successfully"
    });
  } catch (error) {
    next(error);
  }
});

// Delete/deactivate department
router.delete("/:id", validateObjectId("id"), async (req, res, next) => {
  try {
    const { permanent } = req.query;
    
    if (permanent === "true") {
      await Department.findByIdAndDelete(req.params.id);
      res.json({
        success: true,
        message: "Department deleted permanently"
      });
    } else {
      const department = await Department.findByIdAndUpdate(
        req.params.id,
        { isActive: false },
        { new: true }
      );
      
      res.json({
        success: true,
        data: department,
        message: "Department deactivated"
      });
    }
  } catch (error) {
    next(error);
  }
});

// Restore deactivated department
router.patch("/:id/restore", validateObjectId("id"), async (req, res, next) => {
  try {
    const department = await Department.findByIdAndUpdate(
      req.params.id,
      { isActive: true },
      { new: true }
    );

    if (!department) {
      return res.status(404).json({
        success: false,
        error: "Department not found"
      });
    }

    res.json({
      success: true,
      data: department,
      message: "Department restored successfully"
    });
  } catch (error) {
    next(error);
  }
});

export default router;

import mongoose from "mongoose";

const courseDetailSchema = new mongoose.Schema(
  {
    courseCode: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      uppercase: true,
    },
    courseName: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      enum: ["theory", "lab"],
      required: true,
    },
    credits: {
      type: Number,
      min: 1,
      max: 10,
    },
    department: String,
    academicYear: String,
    yearSem: String,
    description: String,
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: true }
);

// Indexes (removed duplicate courseCode index)
courseDetailSchema.index({ type: 1 });
courseDetailSchema.index({ department: 1, academicYear: 1 });

export default mongoose.model("CourseDetail", courseDetailSchema);

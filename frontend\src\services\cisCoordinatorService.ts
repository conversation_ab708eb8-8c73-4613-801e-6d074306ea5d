import apiClient from '../utils/axios';

export interface CISCoordinator {
  _id: string;
  name: string;
  email: string;
  department: string;
  academicYear: string;
  facultyID: string;
}

export interface CreateCISCredentialsRequest {
  departments: string[];
  academicYear: string;
}

export interface AssignCISCoordinatorRequest {
  department: string;
  academicYear: string;
  facultyId: string;
}

export const cisCoordinatorService = {
  // Get all CIS coordinators for a specific academic year
  getCISCoordinators: async (academicYear: string): Promise<CISCoordinator[]> => {
    const response = await apiClient.get(`/api/cis-credentials/coordinators?academicYear=${academicYear}`);
    return response.data as CISCoordinator[];
  },

  // Create CIS credentials for departments
  createCISCredentials: async (data: CreateCISCredentialsRequest): Promise<any> => {
    // Backend expects { Coordinators: [...] }
    const payload = { Coordinators: data.departments, academicYear: data.academicYear };
    const response = await apiClient.post('/api/cis-credentials/create', payload);
    return response.data;
  },

  // Assign faculty to CIS coordinator role
  assignCISCoordinator: async (data: AssignCISCoordinatorRequest): Promise<any> => {
    const response = await apiClient.post('/api/cis-credentials/assign', data);
    return response.data;
  },

  // Reset CIS coordinator password
  resetCISPassword: async (cisCoordinatorId: string): Promise<any> => {
    const response = await apiClient.put(`/api/cis-credentials/reset-password/${cisCoordinatorId}`);
    return response.data;
  },
};

export default cisCoordinatorService;

import express from "express";
import { protect } from "../middleware/authMiddleware.js";
import { isHOD, isHODOrAbove, isAdminOrCIS } from "../middleware/roleMiddleware.js";
import User from "../models/User.js";

const router = express.Router();

router.use(protect);

// Get HODs by academic year
router.get("/hods", isAdminOrCIS, async (req, res, next) => {
  try {
    const { academicYear } = req.query;

    const hods = await User.find({
      role: "HOD",
      isActive: true,
      ...(academicYear && { academicYear })
    })
    .select("name email facultyID department progDept academicYear")
    .sort({ name: 1 });

    res.json(hods);
  } catch (error) {
    console.error("Error fetching HODs:", error);
    next(error);
  }
});

// Create HOD credentials (frontend expects /create)
router.post("/create", isAdminOrCIS, async (req, res, next) => {
  try {
    const { hods } = req.body;

    if (!hods || !Array.isArray(hods)) {
      return res.status(400).json({
        success: false,
        error: "HODs array is required"
      });
    }

    // Process HOD creation (placeholder)
    const results = hods.map(hod => ({
      ...hod,
      status: "created",
      credentials: {
        username: hod.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} HOD credentials created`
    });
  } catch (error) {
    console.error("Error creating HOD credentials:", error);
    next(error);
  }
});

// Alternative endpoint name
router.post("/create-credentials", isAdminOrCIS, async (req, res, next) => {
  try {
    const { hods } = req.body;

    if (!hods || !Array.isArray(hods)) {
      return res.status(400).json({
        success: false,
        error: "HODs array is required"
      });
    }

    // Process HOD creation (placeholder)
    const results = hods.map(hod => ({
      ...hod,
      status: "created",
      credentials: {
        username: hod.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} HOD credentials created`
    });
  } catch (error) {
    console.error("Error creating HOD credentials:", error);
    next(error);
  }
});

// HOD routes - placeholder implementation
router.get("/", isHOD, async (req, res) => {
  res.json({
    success: true,
    data: [],
    message: "HOD credentials endpoint"
  });
});

router.post("/", isHODOrAbove, async (req, res) => {
  res.json({
    success: true,
    message: "HOD credential created"
  });
});

export default router;

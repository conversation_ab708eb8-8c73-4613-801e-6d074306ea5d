import mongoose from "mongoose";

const studentMarkSchema = new mongoose.Schema({
  studentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  rollNumber: String,
  name: String,
  marks: {
    type: Number,
    min: 0,
  },
  isAbsent: {
    type: Boolean,
    default: false,
  },
}, { _id: false });

const componentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  maxMarks: {
    type: Number,
    required: true,
    min: 1,
  },
  studentMarks: [studentMarkSchema],
}, { _id: false });

const gradesheetSchema = new mongoose.Schema(
  {
    courseOfferingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CourseOffering",
      required: true,
    },
    
    // Type: theory or lab
    type: {
      type: String,
      enum: ["theory", "lab"],
      required: true,
    },
    
    // For theory: division, for lab: batch
    division: String,
    batch: String,
    
    // Components (IA1, IA2, ESE, etc.)
    components: [componentSchema],
    
    // Submission status
    submitted: {
      type: Boolean,
      default: false,
    },
    submittedAt: Date,
    
    // Faculty information
    examinerInternal: String,
    examinerExternal: String,
    facultyIncharge: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    
    // Academic info
    academicYear: String,
    yearSem: String,
    department: String,
    
    // Metadata
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    
    // Approval workflow
    approvalStatus: {
      type: String,
      enum: ["draft", "submitted", "approved", "rejected"],
      default: "draft",
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    approvedAt: Date,
    
    // Comments/Notes
    comments: String,
    
    // Version control
    version: {
      type: Number,
      default: 1,
    },
  },
  { timestamps: true }
);

// Indexes (removed duplicate compound index)
gradesheetSchema.index({ facultyIncharge: 1 });
gradesheetSchema.index({ academicYear: 1, yearSem: 1 });
gradesheetSchema.index({ department: 1 });
gradesheetSchema.index({ submitted: 1 });

// Compound index for unique gradesheet per course offering + type + division/batch
gradesheetSchema.index(
  { courseOfferingId: 1, type: 1, division: 1, batch: 1 },
  { unique: true, sparse: true }
);

export default mongoose.model("Gradesheet", gradesheetSchema);

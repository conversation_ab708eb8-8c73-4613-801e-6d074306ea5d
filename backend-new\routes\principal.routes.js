import express from "express";
import { protect } from "../middleware/authMiddleware.js";
import { isPrincipal, isPrincipalOrAdmin, requireRole } from "../middleware/roleMiddleware.js";

const router = express.Router();

router.use(protect);

// Principal routes - placeholder implementation
router.get("/", requireRole("Principal", "Admin"), async (req, res) => {
  res.json({
    success: true,
    data: [],
    message: "Principal endpoint"
  });
});

router.post("/", isPrincipalOrAdmin, async (req, res) => {
  res.json({
    success: true,
    message: "Principal data created"
  });
});

export default router;

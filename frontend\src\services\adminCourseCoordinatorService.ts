import axios from "../utils/axios";

export interface CourseCoordinatorCredential {
  _id: string;
  name: string;
  email: string;
  department: string;
  progDept: string;
  academicYear: string;
  facultyID: string;
  courseCode: string;
  courseName: string;
  yearSem: string;
  role: string;
}

export interface AvailableCourse {
  courseCode: string;
  courseName: string;
  yearSem: string;
  type: "theory" | "lab";
  department: string;
}

export interface CreateCredentialsRequest {
  departments: string[];
  academicYear: string;
}

export interface CreateCredentialsResponse {
  message: string;
  credentials: Array<{
    username: string;
    password: string;
    courseCode: string;
    courseName: string;
    department: string;
    academicYear: string;
    userId: string;
  }>;
}

export interface AssignCoordinatorRequest {
  courseCode: string;
  courseName: string;
  academicYear: string;
  department: string;
  facultyId: string;
}

export interface AssignCoordinatorResponse {
  message: string;
  data: {
    courseCoordinator: CourseCoordinatorCredential;
    assignedFaculty: any;
    credentials: {
      username: string;
      password: string;
      email: string;
    };
  };
}

export interface ResetPasswordResponse {
  message: string;
  username: string;
  newPassword: string;
}

class AdminCourseCoordinatorService {
  // Get all Course Coordinators for an academic year and department
  async getCourseCoordinators(
    academicYear: string,
    department?: string
  ): Promise<CourseCoordinatorCredential[]> {
    try {
      console.log("🌐 [Service] getCourseCoordinators called with:", { academicYear, department });
      
      const params = new URLSearchParams({ academicYear });
      if (department) {
        params.append("department", department);
      }

      console.log("🔗 [Service] Making request to:", `/api/course-coordinators/coordinators?${params}`);
      const response = await axios.get(`/api/course-coordinators/coordinators?${params}`);
      
      console.log("✅ [Service] Response received:", {
        status: response.status,
        dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
        data: response.data
      });
      
      return response.data as CourseCoordinatorCredential[];
    } catch (error: any) {
      console.error("❌ [Service] Error fetching course coordinators:", error);
      console.log("🔍 [Service] Error details:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      throw new Error(error.response?.data?.message || "Failed to fetch course coordinators");
    }
  }

  // Get available courses for credential creation
  async getAvailableCourses(department: string, academicYear: string): Promise<AvailableCourse[]> {
    try {
      const params = new URLSearchParams({ department, academicYear });
      const response = await axios.get(`/api/course-coordinators/available-courses?${params}`);
      return response.data as AvailableCourse[];
    } catch (error: any) {
      console.error("Error fetching available courses:", error);
      throw new Error(error.response?.data?.message || "Failed to fetch available courses");
    }
  }

  // Create course coordinator credentials for multiple courses
  async createCredentials(data: CreateCredentialsRequest): Promise<CreateCredentialsResponse> {
    try {
      console.log("🌐 [Service] createCredentials called with:", data);
      console.log("🔗 [Service] Making request to:", "/api/course-coordinators/create");
      // Backend expects { Coordinators: [...] }
      const payload = { Coordinators: data.departments, academicYear: data.academicYear };
      const response = await axios.post("/api/course-coordinators/create", payload);
      console.log("✅ [Service] Create credentials response:", {
        status: response.status,
        credentialsCount: (response.data as any)?.credentials?.length || 0,
        message: (response.data as any)?.message,
        data: response.data
      });
      return response.data as CreateCredentialsResponse;
    } catch (error: any) {
      console.error("❌ [Service] Error creating course coordinator credentials:", error);
      console.log("🔍 [Service] Create error details:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      throw new Error(
        error.response?.data?.message || "Failed to create course coordinator credentials"
      );
    }
  }

  // Assign faculty to course coordinator role
  async assignCourseCoordinator(data: AssignCoordinatorRequest): Promise<AssignCoordinatorResponse> {
    try {
      const response = await axios.post("/api/course-coordinators/assign", data);
      return response.data as AssignCoordinatorResponse;
    } catch (error: any) {
      console.error("Error assigning course coordinator:", error);
      throw new Error(error.response?.data?.message || "Failed to assign course coordinator");
    }
  }

  // Reset course coordinator password
  async resetPassword(courseCoordinatorId: string): Promise<ResetPasswordResponse> {
    try {
      const response = await axios.put(
        `/api/course-coordinators/reset-password/${courseCoordinatorId}`
      );
      return response.data as ResetPasswordResponse;
    } catch (error: any) {
      console.error("Error resetting course coordinator password:", error);
      throw new Error(error.response?.data?.message || "Failed to reset password");
    }
  }

  // Debug methods for development
  async debugCourseData(): Promise<any> {
    try {
      console.log("🌐 [Service] debugCourseData called");
      const response = await axios.get("/api/course-coordinators/debug/courses");
      console.log("✅ [Service] Debug response:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("❌ [Service] Error debugging course data:", error);
      throw new Error(error.response?.data?.message || "Failed to debug course data");
    }
  }

  async seedCourseData(): Promise<any> {
    try {
      console.log("🌐 [Service] seedCourseData called");
      const response = await axios.post("/api/course-coordinators/seed/courses");
      console.log("✅ [Service] Seed response:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("❌ [Service] Error seeding course data:", error);
      throw new Error(error.response?.data?.message || "Failed to seed course data");
    }
  }
}

export default new AdminCourseCoordinatorService();

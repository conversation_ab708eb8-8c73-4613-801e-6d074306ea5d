import express from "express";
import { protect } from "../middleware/authMiddleware.js";
import { isCourseCoordinator, isCoordinatorOrAbove, isAdminOrCIS } from "../middleware/roleMiddleware.js";
import User from "../models/User.js";

const router = express.Router();

router.use(protect);

// Get course coordinators by academic year
router.get("/coordinators", isAdminOrCIS, async (req, res, next) => {
  try {
    const { academicYear } = req.query;

    const coordinators = await User.find({
      role: "Course Coordinator",
      isActive: true,
      ...(academicYear && { academicYear })
    })
    .select("name email facultyID department progDept academicYear courseCode courseName yearSem")
    .sort({ name: 1 });

    res.json(coordinators);
  } catch (error) {
    console.error("Error fetching course coordinators:", error);
    next(error);
  }
});

// Debug endpoint for course data
router.get("/debug/courses", isAdminOrCIS, async (req, res, next) => {
  try {
    // Import CourseDetail model
    const CourseDetail = (await import("../models/CourseDetail.js")).default;

    const courses = await CourseDetail.find({ isActive: true })
      .select("courseCode courseName type credits department academicYear yearSem")
      .sort({ courseCode: 1 });

    res.json({
      success: true,
      totalCourses: courses.length,
      courses: courses,
      debug: {
        timestamp: new Date().toISOString(),
        endpoint: "/debug/courses"
      }
    });
  } catch (error) {
    console.error("Error debugging course data:", error);
    next(error);
  }
});

// Create course coordinator credentials (frontend expects /create)
router.post("/create", isAdminOrCIS, async (req, res, next) => {
  try {
    const { coordinators } = req.body;

    if (!coordinators || !Array.isArray(coordinators)) {
      return res.status(400).json({
        success: false,
        error: "Coordinators array is required"
      });
    }

    // Process coordinator creation (placeholder)
    const results = coordinators.map(coord => ({
      ...coord,
      status: "created",
      credentials: {
        username: coord.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} course coordinator credentials created`
    });
  } catch (error) {
    console.error("Error creating course coordinator credentials:", error);
    next(error);
  }
});

// Alternative endpoint name
router.post("/create-credentials", isAdminOrCIS, async (req, res, next) => {
  try {
    const { coordinators } = req.body;

    if (!coordinators || !Array.isArray(coordinators)) {
      return res.status(400).json({
        success: false,
        error: "Coordinators array is required"
      });
    }

    // Process coordinator creation (placeholder)
    const results = coordinators.map(coord => ({
      ...coord,
      status: "created",
      credentials: {
        username: coord.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} course coordinator credentials created`
    });
  } catch (error) {
    console.error("Error creating course coordinator credentials:", error);
    next(error);
  }
});

// Course Coordinator routes - placeholder implementation
router.get("/", isCourseCoordinator, async (req, res) => {
  res.json({
    success: true,
    data: [],
    message: "Course Coordinator endpoint"
  });
});

router.post("/", isCoordinatorOrAbove, async (req, res) => {
  res.json({
    success: true,
    message: "Course Coordinator data created"
  });
});

export default router;

import express from "express";
import { protect } from "../middleware/authMiddleware.js";
import { isCIS, isAdminOrCIS } from "../middleware/roleMiddleware.js";
import User from "../models/User.js";

const router = express.Router();

router.use(protect);

// Get CIS coordinators by academic year
router.get("/coordinators", isAdminOrCIS, async (req, res, next) => {
  try {
    const { academicYear } = req.query;

    const coordinators = await User.find({
      role: "CIS Coordinator",
      isActive: true,
      ...(academicYear && { academicYear })
    })
    .select("name email facultyID department progDept academicYear")
    .sort({ name: 1 });

    res.json(coordinators);
  } catch (error) {
    console.error("Error fetching CIS coordinators:", error);
    next(error);
  }
});

// Create CIS coordinator credentials (frontend expects /create)
router.post("/create", isAdminOrCIS, async (req, res, next) => {
  try {
    const { coordinators } = req.body;

    if (!coordinators || !Array.isArray(coordinators)) {
      return res.status(400).json({
        success: false,
        error: "Coordinators array is required"
      });
    }

    // Process CIS coordinator creation (placeholder)
    const results = coordinators.map(coord => ({
      ...coord,
      status: "created",
      credentials: {
        username: coord.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} CIS coordinator credentials created`
    });
  } catch (error) {
    console.error("Error creating CIS coordinator credentials:", error);
    next(error);
  }
});

// Alternative endpoint name
router.post("/create-credentials", isAdminOrCIS, async (req, res, next) => {
  try {
    const { coordinators } = req.body;

    if (!coordinators || !Array.isArray(coordinators)) {
      return res.status(400).json({
        success: false,
        error: "Coordinators array is required"
      });
    }

    // Process CIS coordinator creation (placeholder)
    const results = coordinators.map(coord => ({
      ...coord,
      status: "created",
      credentials: {
        username: coord.email,
        password: "TempPass123!"
      }
    }));

    res.json({
      success: true,
      data: results,
      message: `${results.length} CIS coordinator credentials created`
    });
  } catch (error) {
    console.error("Error creating CIS coordinator credentials:", error);
    next(error);
  }
});

// CIS Credential routes - placeholder implementation
router.get("/", isCIS, async (req, res) => {
  res.json({
    success: true,
    data: [],
    message: "CIS credentials endpoint"
  });
});

router.post("/", isAdminOrCIS, async (req, res) => {
  res.json({
    success: true,
    message: "CIS credential created"
  });
});

export default router;

import User from "../models/User.js";
import CourseOffering from "../models/CourseOffering.js";
import { paginateQuery } from "../utils/pagination.js";
import bcrypt from "bcryptjs";

// Get faculty list (simple list for dropdowns)
export const getFacultyList = async (req, res, next) => {
  try {
    const faculty = await User.find({
      role: { $in: ["Faculty", "Course Coordinator", "CIS Coordinator", "HOD", "Principal", "Exam Cell"] },
      isActive: true
    })
    .select("name facultyID department")
    .sort({ name: 1 });

    res.json(faculty);
  } catch (error) {
    console.error("Error fetching faculty list:", error);
    next(error);
  }
};

// Get all faculty (admin view with pagination)
export const getAllFaculty = async (req, res, next) => {
  try {
    const result = await paginateQuery(User, {
      ...req.query,
      role: { $in: ["Faculty", "Course Coordinator", "CIS Coordinator", "HOD", "Principal", "Exam Cell", "Admin"] }
    }, {
      allowedFilters: [
        'department', 'progDept', 'role', 'isActive', 'search'
      ],
      allowedSortFields: [
        'name', 'facultyID', 'email', 'department', 'role', 'createdAt', 'updatedAt'
      ],
      defaultSort: { name: 1 },
      select: '-passwordHash -passwordResetToken -passwordResetExpires'
    });

    res.json(result);
  } catch (error) {
    console.error("Error fetching all faculty:", error);
    next(error);
  }
};

// Get coordinators list
export const getCoordinatorsList = async (req, res, next) => {
  try {
    const coordinators = await User.find({
      role: { $in: ["Course Coordinator", "CIS Coordinator"] },
      isActive: true
    })
    .select("name facultyID department role courseCode courseName yearSem")
    .sort({ name: 1 });

    res.json({
      success: true,
      data: coordinators,
    });
  } catch (error) {
    console.error("Error fetching coordinators:", error);
    next(error);
  }
};

// Get faculty with their assigned courses
export const getFacultyWithCourses = async (req, res, next) => {
  try {
    const faculty = await User.find({
      role: { $in: ["Faculty", "Course Coordinator"] },
      isActive: true
    })
    .populate('facultyProfile.teachingOfferings', 'courseDetail academicYear yearSem')
    .populate('facultyProfile.coordinatingOfferings', 'courseDetail academicYear yearSem')
    .select("name facultyID department role facultyProfile")
    .sort({ name: 1 });

    res.json({
      success: true,
      data: faculty,
    });
  } catch (error) {
    console.error("Error fetching faculty with courses:", error);
    next(error);
  }
};

// Get faculty by department with courses
export const getFacultyByDepartment = async (req, res, next) => {
  try {
    const { department } = req.params;

    const faculty = await User.find({
      role: { $in: ["Faculty", "Course Coordinator"] },
      $or: [
        { department: department },
        { progDept: department }
      ],
      isActive: true
    })
    .populate('facultyProfile.teachingOfferings')
    .populate('facultyProfile.coordinatingOfferings')
    .select("name facultyID department progDept role facultyProfile")
    .sort({ name: 1 });

    res.json({
      success: true,
      data: faculty,
    });
  } catch (error) {
    console.error("Error fetching faculty by department:", error);
    next(error);
  }
};

// Create faculty
export const createFaculty = async (req, res, next) => {
  try {
    const { password, ...facultyData } = req.body;

    // Hash password if provided
    if (password) {
      facultyData.passwordHash = await bcrypt.hash(password, 12);
    }

    facultyData.createdBy = req.user._id;

    const faculty = new User(facultyData);
    await faculty.save();

    // Remove password from response
    const facultyResponse = faculty.toObject();
    delete facultyResponse.passwordHash;

    res.status(201).json({
      success: true,
      data: facultyResponse,
      message: "Faculty created successfully",
    });
  } catch (error) {
    console.error("Error creating faculty:", error);
    next(error);
  }
};

// Update faculty
export const updateFaculty = async (req, res, next) => {
  try {
    const { password, ...updateData } = req.body;

    const faculty = await User.findById(req.params.id);
    if (!faculty) {
      return res.status(404).json({
        success: false,
        error: "Faculty not found",
      });
    }

    // Hash password if provided
    if (password) {
      updateData.passwordHash = await bcrypt.hash(password, 12);
    }

    Object.assign(faculty, updateData);
    await faculty.save();

    // Remove password from response
    const facultyResponse = faculty.toObject();
    delete facultyResponse.passwordHash;

    res.json({
      success: true,
      data: facultyResponse,
      message: "Faculty updated successfully",
    });
  } catch (error) {
    console.error("Error updating faculty:", error);
    next(error);
  }
};

// Delete faculty
export const deleteFaculty = async (req, res, next) => {
  try {
    const faculty = await User.findById(req.params.id);
    if (!faculty) {
      return res.status(404).json({
        success: false,
        error: "Faculty not found",
      });
    }

    // Check if faculty has any course assignments
    const courseAssignments = await CourseOffering.countDocuments({
      $or: [
        { facultyIncharge: faculty._id },
        { courseCoordinator: faculty._id }
      ]
    });

    if (courseAssignments > 0) {
      return res.status(400).json({
        success: false,
        error: "Cannot delete faculty with active course assignments. Please reassign courses first.",
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: "Faculty deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting faculty:", error);
    next(error);
  }
};

// Toggle faculty status (activate/deactivate)
export const toggleFacultyStatus = async (req, res, next) => {
  try {
    const faculty = await User.findById(req.params.id);
    if (!faculty) {
      return res.status(404).json({
        success: false,
        error: "Faculty not found",
      });
    }

    faculty.isActive = !faculty.isActive;
    await faculty.save();

    res.json({
      success: true,
      data: {
        id: faculty._id,
        isActive: faculty.isActive,
      },
      message: `Faculty ${faculty.isActive ? 'activated' : 'deactivated'} successfully`,
    });
  } catch (error) {
    console.error("Error toggling faculty status:", error);
    next(error);
  }
};

// Get faculty by ID
export const getFacultyById = async (req, res, next) => {
  try {
    const faculty = await User.findById(req.params.id)
      .populate('facultyProfile.teachingOfferings')
      .populate('facultyProfile.coordinatingOfferings')
      .select('-passwordHash -passwordResetToken -passwordResetExpires');

    if (!faculty) {
      return res.status(404).json({
        success: false,
        error: "Faculty not found",
      });
    }

    res.json({
      success: true,
      data: faculty,
    });
  } catch (error) {
    console.error("Error fetching faculty:", error);
    next(error);
  }
};

// Assign course to faculty
export const assignCourseToFaculty = async (req, res, next) => {
  try {
    const { facultyId, courseOfferingId, type } = req.body; // type: 'teaching' or 'coordinating'

    const faculty = await User.findById(facultyId);
    const courseOffering = await CourseOffering.findById(courseOfferingId);

    if (!faculty || !courseOffering) {
      return res.status(404).json({
        success: false,
        error: "Faculty or course offering not found",
      });
    }

    // Initialize facultyProfile if it doesn't exist
    if (!faculty.facultyProfile) {
      faculty.facultyProfile = {
        teachingOfferings: [],
        coordinatingOfferings: []
      };
    }

    // Add to appropriate array
    if (type === 'teaching') {
      if (!faculty.facultyProfile.teachingOfferings.includes(courseOfferingId)) {
        faculty.facultyProfile.teachingOfferings.push(courseOfferingId);
        courseOffering.facultyIncharge = facultyId;
      }
    } else if (type === 'coordinating') {
      if (!faculty.facultyProfile.coordinatingOfferings.includes(courseOfferingId)) {
        faculty.facultyProfile.coordinatingOfferings.push(courseOfferingId);
        courseOffering.courseCoordinator = facultyId;
      }
    }

    await Promise.all([faculty.save(), courseOffering.save()]);

    res.json({
      success: true,
      message: "Course assigned successfully",
    });
  } catch (error) {
    console.error("Error assigning course:", error);
    next(error);
  }
};
